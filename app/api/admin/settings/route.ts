import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { checkIsAdmin, createAdminClient } from '@/utils/supabase/admin';

export async function GET() {
  try {
    const supabase = await createClient();
    
    const { data: settings, error } = await supabase
      .from('app_settings')
      .select('*')
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({ settings });
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json({ error: 'Failed to fetch settings' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const isAdmin = await checkIsAdmin(user.id);
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { registration_mode } = await request.json();

    if (!registration_mode || !['open', 'approval_required'].includes(registration_mode)) {
      return NextResponse.json({ error: 'Invalid registration mode' }, { status: 400 });
    }

    // Use admin client to bypass RLS policies
    const adminSupabase = createAdminClient();
    const { error } = await adminSupabase
      .from('app_settings')
      .update({ registration_mode })
      .eq('id', 1);

    if (error) {
      throw error;
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json({ error: 'Failed to update settings' }, { status: 500 });
  }
}