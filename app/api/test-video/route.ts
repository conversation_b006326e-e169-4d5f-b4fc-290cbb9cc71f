import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  console.log('[TEST-VIDEO] Starting test video generation...');
  
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.log('[TEST-VIDEO] No authenticated user');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('[TEST-VIDEO] User authenticated:', user.email);

    // Check environment variables
    const apiKey = process.env.AIHUBMIX_API_KEY;
    if (!apiKey) {
      console.error('[TEST-VIDEO] Missing AIHUBMIX_API_KEY');
      return NextResponse.json({ error: 'Missing API key configuration' }, { status: 500 });
    }

    console.log('[TEST-VIDEO] API key found, length:', apiKey.length);

    const { prompt } = await request.json();
    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
    }

    console.log('[TEST-VIDEO] Prompt received:', prompt.substring(0, 50) + '...');

    // Test API call with timeout
    console.log('[TEST-VIDEO] Making API call to AiHubMix...');
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const response = await fetch('https://api.aihubmix.com/v1/video/generation', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt,
          model: 'kling',
          aspect_ratio: '16:9',
          duration: 5
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log('[TEST-VIDEO] API response status:', response.status);
      console.log('[TEST-VIDEO] API response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[TEST-VIDEO] API error response:', errorText);
        return NextResponse.json({ 
          error: 'Video generation failed', 
          details: errorText,
          status: response.status 
        }, { status: 500 });
      }

      const result = await response.json();
      console.log('[TEST-VIDEO] API success, result keys:', Object.keys(result));

      return NextResponse.json({
        success: true,
        message: 'Test video generation successful',
        result: result,
        timestamp: new Date().toISOString()
      });

    } catch (fetchError) {
      clearTimeout(timeoutId);
      
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('[TEST-VIDEO] Request timeout');
        return NextResponse.json({ error: 'Request timeout after 30 seconds' }, { status: 504 });
      }
      
      console.error('[TEST-VIDEO] Fetch error:', fetchError);
      return NextResponse.json({ 
        error: 'Network error', 
        details: fetchError instanceof Error ? fetchError.message : 'Unknown error'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('[TEST-VIDEO] Unexpected error:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
